extends ../layout

block content
  .container
    .d-flex.justify-content-between.align-items-center.mb-4
      h1.h2 Expense Details
      .btn-group
        a.btn.btn-outline-secondary(href=`/budget/expense/${expense._id}/edit`)
          i.bi.bi-pencil.me-1
          | Edit
        a.btn.btn-outline-primary(href='/budget/expenses')
          i.bi.bi-arrow-left.me-1
          | Back

    // Expense details card
    .card.mb-4
      .card-body
        .row
          .col-md-8
            h3.card-title.mb-3= expense.description
            
            if expense.notes
              .mb-3
                h5 Notes
                p.text-muted= expense.notes
            
            .row
              .col-sm-6.mb-3
                h6.text-muted.mb-1 Category
                div
                  span.badge.rounded-pill.me-2(style=`background-color: ${expense.category.color}`)
                  | #{expense.category.name}
              .col-sm-6.mb-3
                h6.text-muted.mb-1 Date
                p.mb-0= expense.date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })
              .col-sm-6.mb-3
                h6.text-muted.mb-1 Created
                p.mb-0= expense.createdAt.toLocaleDateString()
              .col-sm-6.mb-3
                h6.text-muted.mb-1 Last Updated
                p.mb-0= expense.updatedAt.toLocaleDateString()

          .col-md-4
            .card.bg-primary.text-white.text-center
              .card-body
                h5.card-title Amount
                h2.mb-0 R#{expense.amount.toFixed(2)}

    // Related information
    .row.mb-4
      .col-md-6
        .card.h-100
          .card-header
            h5.mb-0
              i.bi.bi-tags.me-2
              | Category Information
          .card-body
            h6= expense.category.name
            if expense.category.description
              p.text-muted.mb-3= expense.category.description
            else
              p.text-muted.mb-3.fst-italic No category description
            
            .d-flex.justify-content-between.align-items-center
              small.text-muted Category Color:
              .rounded-circle(style=`background-color: ${expense.category.color}; width: 25px; height: 25px;`)
            
            .mt-3
              a.btn.btn-sm.btn-outline-primary(href=`/budget/category/${expense.category._id}`)
                i.bi.bi-eye.me-1
                | View Category

      .col-md-6
        .card.h-100
          .card-header
            h5.mb-0
              i.bi.bi-calendar-event.me-2
              | Timeline
          .card-body
            .timeline
              .timeline-item.mb-3
                .d-flex.align-items-center
                  .timeline-marker.bg-success.rounded-circle.me-3(style="width: 12px; height: 12px;")
                  div
                    h6.mb-1 Expense Created
                    small.text-muted= expense.createdAt.toLocaleDateString()
              
              if expense.updatedAt.getTime() !== expense.createdAt.getTime()
                .timeline-item.mb-3
                  .d-flex.align-items-center
                    .timeline-marker.bg-info.rounded-circle.me-3(style="width: 12px; height: 12px;")
                    div
                      h6.mb-1 Last Modified
                      small.text-muted= expense.updatedAt.toLocaleDateString()

    // Actions
    .card.bg-light
      .card-body
        h5.card-title
          i.bi.bi-lightning.me-2
          | Actions
        .d-flex.gap-2.flex-wrap
          a.btn.btn-outline-primary(href=`/budget/expense/${expense._id}/edit`)
            i.bi.bi-pencil.me-1
            | Edit Expense
          a.btn.btn-outline-secondary(href=`/budget/category/${expense.category._id}`)
            i.bi.bi-tags.me-1
            | View Category
          a.btn.btn-outline-info(href='/budget/expense/create')
            i.bi.bi-plus-circle.me-1
            | Add New Expense
          
          // Delete button with confirmation
          .ms-auto
            form.d-inline(method='POST', action=`/budget/expense/${expense._id}/delete`)
              button.btn.btn-outline-danger(
                type='submit',
                data-confirm-delete='Are you sure you want to delete this expense? This action cannot be undone.'
              )
                i.bi.bi-trash.me-1
                | Delete

block scripts
  script.
    // Confirmation dialog for delete
    document.addEventListener('DOMContentLoaded', function() {
      const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
      deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          const message = this.getAttribute('data-confirm-delete');
          if (!confirm(message)) {
            e.preventDefault();
          }
        });
      });
    });
