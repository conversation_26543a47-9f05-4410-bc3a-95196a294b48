extends ../layout

block content
  .d-flex.justify-content-between.align-items-center.mb-4
    h1.h2 Categories
    a.btn.btn-primary(href='/budget/category/create')
      i.bi.bi-plus-circle.me-1
      | Create Category

  if error
    .alert.alert-danger= error
  else if categories && categories.length > 0
    .row.g-4
      each category in categories
        .col-md-6.col-lg-4
          .card.h-100
            .card-body
              .d-flex.justify-content-between.align-items-start.mb-3
                div
                  .d-flex.align-items-center.mb-2
                    .rounded-circle.me-2(
                      style=`background-color: ${category.color}; width: 20px; height: 20px;`
                    )
                    h5.card-title.mb-0= category.name
                .dropdown
                  button.btn.btn-sm.btn-outline-secondary.dropdown-toggle(type='button', data-bs-toggle='dropdown')
                    i.bi.bi-three-dots
                  ul.dropdown-menu
                    li
                      a.dropdown-item(href=`/budget/category/${category._id}`)
                        i.bi.bi-eye.me-1
                        | View Details
                    li
                       a.dropdown-item(href=`/budget/category/${category._id}/edit`)
                        i.bi.bi-pencil.me-1
                        | Edit
                    li.dropdown-divider
                    li
                      form(method='POST', action=`/budget/category/${category._id}/delete`)
                        button.dropdown-item.text-danger(
                          type='submit',
                          data-confirm-delete='Are you sure you want to delete this category?',
                          disabled=category.expenseCount > 0 || category.budgetCount > 0
                        )
                          i.bi.bi-trash.me-1
                          | Delete

              if category.description
                p.card-text.text-muted= category.description
              else
                p.card-text.text-muted.fst-italic No description

              .row.text-center.mt-3
                .col-6
                  .border-end
                    .h5.mb-0.text-primary= category.expenseCount
                    .small.text-muted Expenses
                .col-6
                  .h5.mb-0.text-success= category.budgetCount
                  .small.text-muted Budgets

            .card-footer.bg-transparent
              .d-flex.justify-content-between.align-items-center
                small.text-muted
                  i.bi.bi-calendar.me-1
                  | Created #{category.createdAt.toLocaleDateString()}
                if category.expenseCount > 0 || category.budgetCount > 0
                  small.text-warning
                    i.bi.bi-info-circle.me-1
                    | In use

  else
    .text-center.py-5
      i.bi.bi-tags.text-muted.display-1
      h3.mt-3.text-muted No Categories Yet
      p.text-muted.mb-4 Create categories to organize your expenses and budgets.
      a.btn.btn-primary.btn-lg(href='/budget/category/create')
        i.bi.bi-plus-circle.me-2
        | Create Your First Category

  // Quick category creation
  if categories && categories.length > 0
    .mt-5
      .card.bg-light
        .card-body
          h5.card-title
            i.bi.bi-lightbulb.me-2
            | Quick Tips
          ul.mb-0
            li Use different colors to easily identify categories
            li Create specific categories for better expense tracking
            li Categories with expenses or budgets cannot be deleted
            li Consider categories like: Food, Transportation, Entertainment, Bills
