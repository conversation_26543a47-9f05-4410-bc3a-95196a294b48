extends ../layout

block content
  .d-flex.justify-content-between.align-items-center.mb-4
    h1.h2 Expenses
    a.btn.btn-primary(href='/budget/expense/create')
      i.bi.bi-plus-circle.me-1
      | Add Expense

  // Search and filters
  .row.mb-4
    .col-md-6
      .input-group
        span.input-group-text
          i.bi.bi-search
        input.form-control(
          type='text',
          placeholder='Search expenses...',
          data-search='tbody tr'
        )
    .col-md-6.text-md-end
      .text-muted
        | Total: #{totalExpenses} expenses

  if error
    .alert.alert-danger= error
  else if expenses && expenses.length > 0
    .card
      .table-responsive
        table.table.table-hover.mb-0
          thead.table-light
            tr
              th Date
              th Description
              th Category
              th Amount
              th Payment Method
              th Actions

          tbody
            each expense in expenses
              tr
                td= expense.date.toLocaleDateString()
                td
                  .fw-medium= expense.description
                  if expense.notes
                    .small.text-muted= expense.notes
                td
                  span.badge.rounded-pill(style=`background-color: ${expense.category.color}`)= expense.category.name
                td.text-end.fw-bold R#{expense.amount.toFixed(2)}
                td
                  span.badge.bg-secondary= expense.payment_method.replace('_', ' ').toUpperCase()
                td
                  .btn-group.btn-group-sm(role='group')
                    a.btn.btn-outline-primary(href=`/budget/expense/${expense._id}`, title='View')
                      i.bi.bi-eye
                    a.btn.btn-outline-secondary(href=`/budget/expense/${expense._id}/edit`, title='Edit')
                      i.bi.bi-pencil
                    form.d-inline(method='POST', action=`/budget/expense/${expense._id}/delete`)
                      button.btn.btn-outline-danger(
                        type='submit',
                        title='Delete',
                        data-confirm-delete='Are you sure you want to delete this expense?'
                      )
                        i.bi.bi-trash

    // Pagination
    if pagination.totalPages > 1
      nav.mt-4
        ul.pagination.justify-content-center
          if pagination.hasPrev
            li.page-item
              a.page-link(href=`?page=${pagination.prevPage}`)
                i.bi.bi-chevron-left
                | Previous
          else
            li.page-item.disabled
              span.page-link Previous

          - for (let i = 1; i <= pagination.totalPages; i++)
            li.page-item(class=i === pagination.currentPage ? 'active' : '')
              a.page-link(href=`?page=${i}`)= i

          if pagination.hasNext
            li.page-item
              a.page-link(href=`?page=${pagination.nextPage}`)
                | Next
                i.bi.bi-chevron-right
          else
            li.page-item.disabled
              span.page-link Next

  else
    .text-center.py-5
      i.bi.bi-receipt.text-muted.display-1
      h3.mt-3.text-muted No Expenses Yet
      p.text-muted.mb-4 Start tracking your spending by adding your first expense.
      a.btn.btn-primary.btn-lg(href='/budget/expense/create')
        i.bi.bi-plus-circle.me-2
        | Add Your First Expense
