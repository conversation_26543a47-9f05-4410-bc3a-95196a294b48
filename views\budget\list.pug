extends ../layout

block content
  .d-flex.justify-content-between.align-items-center.mb-4
    h1.h2 Budgets
    a.btn.btn-primary(href='/budget/create')
      i.bi.bi-plus-circle.me-1
      | Create Budget

  if error
    .alert.alert-danger= error
  else if budgets && budgets.length > 0
    .row.g-4
      each budget in budgets
        .col-md-6.col-lg-4
          .card.h-100
            .card-body
              .d-flex.justify-content-between.align-items-start.mb-3
                div
                  h5.card-title.mb-1= budget.name
                  span.badge.rounded-pill(style=`background-color: ${budget.category.color}`)= budget.category.name
                .dropdown
                  button.btn.btn-sm.btn-outline-secondary.dropdown-toggle(type='button', data-bs-toggle='dropdown')
                    i.bi.bi-three-dots
                  ul.dropdown-menu
                    li
                      a.dropdown-item(href=`/budget/budget/${budget._id}`)
                        i.bi.bi-eye.me-1
                        | View Details
                    li
                      a.dropdown-item(href=`/budget/budget/${budget._id}/edit`)
                        i.bi.bi-pencil.me-1
                        | Edit
                    li.dropdown-divider
                    li
                      form(method='POST', action=`/budget/budget/${budget._id}/delete`)
                        button.dropdown-item.text-danger(type='submit', data-confirm-delete='Are you sure you want to delete this budget?')
                          i.bi.bi-trash.me-1
                          | Delete

              .mb-3
                .d-flex.justify-content-between.align-items-center.mb-1
                  span.text-muted Budget Amount
                  span.fw-bold R#{budget.amount.toFixed(2)}
                
                .d-flex.justify-content-between.align-items-center.mb-1
                  span.text-muted Spent
                  span R#{budget.spent}
                
                .d-flex.justify-content-between.align-items-center.mb-2
                  span.text-muted Remaining
                  span(class=budget.remaining < 0 ? 'text-danger fw-bold' : 'text-success') R#{budget.remaining}

              .mb-3
                .d-flex.justify-content-between.align-items-center.mb-1
                  small.text-muted Progress
                  small.text-muted #{budget.progress}%
                
                .progress(style='height: 8px;')
                  - const progressClass = budget.progress > 90 ? 'bg-danger' : budget.progress > 75 ? 'bg-warning' : 'bg-success'
                  .progress-bar(
                    class=progressClass,
                    style=`width: ${Math.min(budget.progress, 100)}%`
                  )

              .d-flex.justify-content-between.align-items-center.text-muted.small
                span
                  i.bi.bi-calendar.me-1
                  = budget.start_date.toLocaleDateString()
                span
                  i.bi.bi-calendar-check.me-1
                  = budget.end_date.toLocaleDateString()

            .card-footer.bg-transparent
              .d-flex.justify-content-between.align-items-center
                small.text-muted
                  = budget.period.charAt(0).toUpperCase() + budget.period.slice(1)
                  |  Budget
                if budget.days_remaining !== undefined
                  small.text-muted
                    = budget.days_remaining
                    |  days left

  else
    .text-center.py-5
      i.bi.bi-piggy-bank.text-muted.display-1
      h3.mt-3.text-muted No Budgets Yet
      p.text-muted.mb-4 Create your first budget to start tracking your spending goals.
      a.btn.btn-primary.btn-lg(href='/budget/create')
        i.bi.bi-plus-circle.me-2
        | Create Your First Budget
